<script setup lang="tsx">
import { NTag } from 'naive-ui';
import type { ProDataTableColumns, ProSearchFormColumns } from 'pro-naive-ui';
import { createProSearchForm, renderProDateText, useNDataTable, useRequest } from 'pro-naive-ui';
import { reactive, ref, watch } from 'vue';

import { renderModal } from '@/components/re-modal';
import { roleBtn } from '@/directives/permission/permi-btn';
import { useUrlSearchParamsSync } from '@/hooks/common/use-url-search-params';
import { $t } from '@/locales';
import { fetchGetContactList } from '@/service/api/hrm/roster_file';
import { hrmBaseUrl } from '@/service/request';
import { getToken } from '@/store/modules/auth/shared';
import { getAllPositionOptions } from '@/utils/async-functions';
import { formatTimestampRange } from '@/utils/date-format';
import { getProxyUrl } from '@/utils/get-file-url';
import PreviewInfo from '@/views/hrm/roster_review/modules/preview-info.vue';

// 定义搜索参数的初始值
const searchParams = reactive({
  employeeName: null,
  employeeDeptId: null,
  employeePositionId: null,
  createTime: null
});

// 集成 URL 参数同步
const { clearUrlParams } = useUrlSearchParamsSync(searchParams, {
  excludeKeys: ['pageNo', 'pageSize'],
  debounceDelay: 300
});

// 添加调试日志
watch(
  searchParams,
  newParams => {
    console.log('搜索参数变化:', newParams);
  },
  { deep: true }
);

const searchForm = createProSearchForm({
  initialValues: searchParams,
  onReset: () => {
    // 重置搜索参数
    Object.assign(searchParams, {
      employeeName: null,
      employeeDeptId: null,
      employeePositionId: null,
      createTime: null
    });
    // 清除 URL 参数
    clearUrlParams();
  },
  onSubmit: () => {
    // 搜索时更新参数，URL 会自动同步
    Object.assign(searchParams, searchForm.fieldsValue.value);
  }
});

const { data: positionOptions } = useRequest(getAllPositionOptions);
const {
  table: { tableProps },
  search: { proSearchFormProps }
} = useNDataTable(({ current, pageSize }, values) => fetchList({ pageNo: current, pageSize }, values), {
  form: searchForm
});

const columns = ref<ProDataTableColumns<Api.Hrm.ContractListRespVo>>([
  {
    title: '业务分类',
    align: 'center',
    path: 'categoryName'
  },
  {
    title: '员工姓名',
    align: 'center',
    path: 'employeeName'
  },
  {
    title: '部门',
    align: 'center',
    path: 'employeeDept'
  },
  {
    title: '岗位',
    align: 'center',
    path: 'employeePosition'
  },
  {
    title: '合同状态',
    align: 'center',
    path: 'contractStatus.name',
    render: row => <NTag type="info">{row.contractStatus?.name}</NTag>
  },
  {
    title: '创建时间',
    align: 'center',
    render: row =>
      renderProDateText(row.createTime, {
        pattern: 'datetime'
      })
  },
  {
    title: $t('common.operate'),
    width: 180,
    align: 'center',
    render: row => {
      return (
        <div class="flex-center gap-8px">
          {roleBtn('预览合同', ['hrm:contact:update'], 'primary', () => handleProView(row))}
        </div>
      );
    }
  }
]);

async function handleProView(row: Api.Hrm.ContractListRespVo) {
  renderModal(
    PreviewInfo,
    {
      data: getProxyUrl(`${hrmBaseUrl(`/contract/preview/${row.contractId}?token=${getToken()}`)}`),
      hideBtn: true,
      bizTypeLabel: row.categoryName,
      companyLabel: row.companyName
    },
    {
      title: '预览合同',
      style: {
        width: '80%'
      }
    }
  );
}

const searchColumns = ref<
  ProSearchFormColumns<{ employeeName: string; employeeDeptId: number; employeePositionId: number }>
>([
  {
    title: '员工姓名',
    path: 'employeeName'
  },
  {
    title: '所属部门',
    path: 'employeeDeptId',
    field: 'dept-tree-select',
    fieldProps: {
      placeholder: '请选择部门'
    }
  },
  {
    title: '岗位',
    path: 'employeePositionId',
    field: 'select',
    fieldProps() {
      return {
        options: positionOptions.value
      };
    }
  }
]);

async function fetchList(params: any, values: any) {
  const { createTime, ...ret } = values;
  const { data } = await fetchGetContactList({ ...params, ...ret, createTime: formatTimestampRange(createTime) });
  return {
    list: data?.list || [],
    total: data?.total || 0
  };
}
</script>

<template>
  <div class="h-full flex flex-col">
    <ProCard class="mb-10px" :show-collapse="false">
      <ProSearchForm :form="searchForm" :columns="searchColumns" v-bind="proSearchFormProps" />
    </ProCard>
    <ProDataTable
      title="文件管理"
      size="small"
      flex-height
      :columns="columns"
      :row-key="row => row.id"
      v-bind="tableProps"
    />
  </div>
</template>

<style scoped>
:deep(.n-form-item-feedback-wrapper) {
  display: none;
}
</style>
