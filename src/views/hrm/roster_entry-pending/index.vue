<script lang="tsx" setup>
import { NTag } from 'naive-ui';
import type { ProDataTableColumns, ProSearchFormColumns } from 'pro-naive-ui';
import { createProSearchForm, useNDataTable } from 'pro-naive-ui';
import { useRouter } from 'vue-router';

import { renderModal, renderModalBtn } from '@/components/re-modal';
import { roleBtn } from '@/directives/permission/permi-btn';
import { PreEntryStatus } from '@/enum';
import { useDict } from '@/hooks/business/useDict';
import { useRouterWithUrlSync } from '@/hooks/common/use-router-with-url-sync';
import { useUrlSearchParamsSync } from '@/hooks/common/use-url-search-params';
import { $t } from '@/locales';
import { fetchGetPreEntryList } from '@/service/api/hrm/roster_entry-pending';
import { hrmBaseUrl } from '@/service/request';
import { getToken } from '@/store/modules/auth/shared';
import { getAllPositionOptions } from '@/utils/async-functions';
import { getProxyUrl } from '@/utils/get-file-url';
import GiveupForm from '@/views/hrm/roster_entry-pending/modules/give-up-form.vue';
import InvitationForm from '@/views/hrm/roster_entry-pending/modules/invitation-form.vue';
import PreviewInfo from '@/views/hrm/roster_review/modules/preview-info.vue';

const router = useRouter();
const { hrm_pre_entry_status, hrm_position_property } = useDict(['hrm_pre_entry_status', 'hrm_position_property']);

const searchParams = reactive({
  positionId: null,
  deptId: null,
  jobNumber: null,
  name: null,
  subStatus: null,
  positionProperty: null
});

const checkedRowKeys = ref<number[]>([]);

// 集成 URL 参数同步
const { clearUrlParams, syncToUrlImmediate } = useUrlSearchParamsSync(searchParams, {
  excludeKeys: [],
  debounceDelay: 100 // 减少防抖延迟，确保快速同步
});
const { safePush } = useRouterWithUrlSync(syncToUrlImmediate);
const searchForm = createProSearchForm({
  initialValues: searchParams,
  onReset: () => {
    // 重置搜索参数
    Object.assign(searchParams, {
      positionId: null,
      deptId: null,
      jobNumber: null,
      name: null,
      subStatus: null,
      positionProperty: null
    });
    // 清除 URL 参数
    clearUrlParams();
  },
  onSubmit: () => {
    // 搜索时更新参数，URL 会自动同步
    Object.assign(searchParams, searchForm.fieldsValue.value);
  }
});

const {
  table: { tableProps, onChange },
  search: { proSearchFormProps }
} = useNDataTable(
  ({ current, pageSize }, values) => {
    return fetchGetPreEntryList({
      ...values,
      pageSize,
      pageNo: current
    }).then(({ data }) => {
      return {
        list: data?.list || [],
        total: data?.total || 0
      };
    });
  },
  {
    form: searchForm
  }
);

const columns = ref<ProDataTableColumns<Api.Hrm.PreEntryRespVO>>([
  {
    type: 'selection',
    align: 'center',
    width: 64
  },
  {
    type: 'index',
    title: $t('common.index'),
    align: 'center',
    width: 64
  },
  {
    key: 'name',
    title: '姓名',
    align: 'center'
  },
  {
    key: 'dept.name',
    title: '部门',
    align: 'center'
  },
  {
    key: 'position.name',
    title: '职位',
    align: 'center'
  },
  {
    key: 'positionProperty',
    title: '岗位属性',
    align: 'center',
    render: (row: Api.Hrm.PreEntryRespVO) => <PositionPro positionProperty={row.positionProperty} />
  },
  {
    key: 'idNumber',
    title: '身份证号',
    align: 'center'
  },
  {
    key: 'status',
    title: '员工状态',
    align: 'center',
    render: (rowData: Api.Hrm.PreEntryRespVO) => (
      <div>
        <NTag type="success">{rowData.subStatus?.name}</NTag>
      </div>
    )
  },

  {
    key: 'createTime',
    title: '创建时间',
    align: 'center'
  },
  {
    key: 'reminderDateForClassOpening',
    title: '开班时间',
    align: 'center'
  },
  {
    key: 'operate',
    title: $t('common.operate'),
    align: 'center',
    width: 180,
    render: (rowData: Api.Hrm.PreEntryRespVO) => {
      const reviewBtnNameMap: Record<number, string> = {
        [PreEntryStatus.Reviewing]: '审核',
        [PreEntryStatus.PendingContracts]: '发起合同'
      };

      const reviewButtonLabel = reviewBtnNameMap[rowData.subStatusId] || '详情';

      return (
        <div class="flex flex-wrap gap-2">
          {roleBtn(reviewButtonLabel, ['hrm:employee:review'], 'info', () => handleReviewJump(rowData.id))}
          {[PreEntryStatus.WaitingForSign].includes(rowData.subStatusId) && rowData.contractId
            ? roleBtn('预览合同', ['hrm:contact:update'], 'success', () => handleProView(rowData))
            : null}

          {!rowData.inDingTalk
            ? roleBtn('入职邀请', ['hrm:employee:review'], 'success', () => handleInvitation(rowData.id))
            : null}

          {![PreEntryStatus.WaitingForContract].includes(rowData.subStatusId)
            ? roleBtn('放弃入职', ['hrm:onboarding:giveup'], 'info', () => handleGiveup(rowData))
            : null}
        </div>
      );
    }
  }
]);

async function handleInvitation(userIds: number | number[]) {
  renderModalBtn(
    InvitationForm,
    { useIds: Array.isArray(userIds) ? userIds : [userIds], type: Array.isArray(userIds) ? 'multi' : 'single' },
    {
      title: '入职邀请',
      style: {
        width: '50%'
      },
      func: onChange
    }
  );
}

async function handleGiveup(rowData: Api.Hrm.PreEntryRespVO) {
  renderModalBtn(
    GiveupForm,
    { rowData },
    {
      title: '放弃入职',
      style: {
        width: '50%'
      },
      func: onChange
    }
  );
}

/** 处理跳转到审核页面，确保在跳转前同步 URL 参数 */
async function handleReviewJump(id: number) {
  safePush({ path: '/hrm/roster/review', query: { id } });
}

async function handleProView(rowData: Api.Hrm.PreEntryRespVO) {
  renderModal(
    PreviewInfo,
    {
      data: getProxyUrl(`${hrmBaseUrl(`/contract/preview/${rowData.contractId}?token=${getToken()}`)}`),
      hideBtn: true
    },
    {
      title: '预览合同',
      style: {
        width: '80%'
      }
    }
  );
}

const searchColumns = ref<ProSearchFormColumns<Api.Hrm.SimpleEmployeeInfoSearch>>([
  {
    title: '员工姓名',
    path: 'name'
  },
  {
    title: '员工职位',
    path: 'positionId',
    field: 'select-with-search',
    fieldProps: {
      apiFunc: getAllPositionOptions,
      selectedOptions: [],
      pageSize: 0,
      placeholder: '职位'
    }
  },
  {
    title: '状态',
    path: 'subStatus',
    field: 'select',
    fieldProps: {
      options: hrm_pre_entry_status
    }
  } as any,
  {
    title: '岗位属性',
    path: 'positionProperty',
    field: 'select',
    fieldProps: {
      options: hrm_position_property
    }
  },
  {
    title: '所属部门',
    path: 'deptId',
    field: 'dept-tree-select',
    fieldProps: {
      placeholder: '请选择部门'
    }
  }
]);
</script>

<template>
  <div class="min-h-500px flex-col-stretch overflow-hidden lt-sm:overflow-auto">
    <ProCard class="mb-10px" :show-collapse="false" content-class="!pb-[0px]">
      <ProSearchForm :form="searchForm" :columns="searchColumns" v-bind="proSearchFormProps" />
    </ProCard>
    <ProDataTable
      title="文件管理"
      size="small"
      flex-height
      :columns="columns"
      :row-key="row => row.id"
      v-bind="tableProps"
    >
      <template #toolbar>
        <NButton
          size="small"
          ghost
          type="primary"
          :disabled="checkedRowKeys.length === 0"
          class="mr-3"
          @click="handleInvitation(checkedRowKeys)"
        >
          <template #icon>
            <icon-ic-round-check class="text-icon" />
          </template>
          批量入职邀请
        </NButton>
      </template>
    </ProDataTable>
  </div>
</template>

<style scoped></style>
