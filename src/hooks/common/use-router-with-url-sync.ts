import { useRouter } from 'vue-router';
import { nextTick } from 'vue';
import type { RouteLocationRaw } from 'vue-router';

/**
 * 带 URL 参数同步的路由 Hook
 * 在路由跳转前确保当前页面的 URL 参数已同步到地址栏
 */
export function useRouterWithUrlSync(syncToUrlImmediate?: () => void) {
  const router = useRouter();

  /**
   * 安全的路由跳转，确保在跳转前同步 URL 参数
   */
  async function safePush(to: RouteLocationRaw) {
    // 如果提供了同步函数，先同步 URL 参数
    if (syncToUrlImmediate) {
      syncToUrlImmediate();
      // 等待一个微任务周期确保 URL 更新完成
      await nextTick();
    }
    
    return router.push(to);
  }

  /**
   * 安全的路由替换，确保在替换前同步 URL 参数
   */
  async function safeReplace(to: RouteLocationRaw) {
    // 如果提供了同步函数，先同步 URL 参数
    if (syncToUrlImmediate) {
      syncToUrlImmediate();
      // 等待一个微任务周期确保 URL 更新完成
      await nextTick();
    }
    
    return router.replace(to);
  }

  return {
    router,
    safePush,
    safeReplace,
    // 导出原始方法以备需要
    push: router.push,
    replace: router.replace,
    go: router.go,
    back: router.back,
    forward: router.forward
  };
}
