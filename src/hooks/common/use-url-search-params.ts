import { useDebounceFn, useUrlSearchParams } from '@vueuse/core';
import { nextTick, onMounted, ref, watch } from 'vue';

interface UseUrlSearchParamsOptions {
  /** 排除的参数键，这些参数不会同步到 URL */
  excludeKeys?: string[];
  /** 防抖延迟时间（毫秒） */
  debounceDelay?: number;
  /** 是否移除空值 */
  removeNullishValues?: boolean;
  /** 是否移除假值 */
  removeFalsyValues?: boolean;
  /** 是否立即从 URL 恢复参数 */
  immediate?: boolean;
}

/**
 * URL 搜索参数同步 Hook
 * 用于将搜索表单参数与 URL 查询参数进行双向同步
 */
export function useUrlSearchParamsSync<T extends Record<string, any>>(
  searchParams: T,
  options: UseUrlSearchParamsOptions = {}
) {
  const {
    excludeKeys = ['pageNo', 'pageSize'],
    debounceDelay = 300,
    removeNullishValues = true,
    removeFalsyValues = false,
    immediate = true
  } = options;

  // 使用 VueUse 的 URL 参数管理
  const urlParams = useUrlSearchParams('history', {
    removeNullishValues,
    removeFalsyValues
  });

  // 标记是否已初始化，避免初始化时触发不必要的同步
  const isInitialized = ref(false);

  /**
   * 从 URL 恢复搜索参数
   */
  function restoreFromUrl() {
    Object.keys(searchParams).forEach(key => {
      if (excludeKeys.includes(key)) return;

      const urlValue = urlParams[key];
      if (urlValue !== undefined && urlValue !== null && urlValue !== '') {
        try {
          // 尝试解析 JSON（用于复杂类型）
          if (typeof urlValue === 'string' && (urlValue.startsWith('{') || urlValue.startsWith('['))) {
            (searchParams as any)[key] = JSON.parse(urlValue);
          } else {
            // 处理基本类型
            const originalValue = (searchParams as any)[key];
            if (typeof originalValue === 'number') {
              (searchParams as any)[key] = Number(urlValue);
            } else if (typeof originalValue === 'boolean') {
              (searchParams as any)[key] = urlValue === 'true';
            } else {
              (searchParams as any)[key] = urlValue;
            }
          }
        } catch (error) {
          console.warn(`Failed to parse URL parameter ${key}:`, error);
          (searchParams as any)[key] = urlValue;
        }
      }
      console.log('搜索参数变化:', searchParams);
    });
  }

  /**
   * 将搜索参数同步到 URL（防抖处理）
   */
  const syncToUrl = useDebounceFn(() => {
    Object.keys(searchParams).forEach(key => {
      if (excludeKeys.includes(key)) return;

      const value = (searchParams as any)[key];
      if (value !== undefined && value !== null && value !== '') {
        // 序列化复杂类型
        if (typeof value === 'object') {
          urlParams[key] = JSON.stringify(value);
        } else {
          urlParams[key] = String(value);
        }
      } else {
        // 删除空值参数
        delete urlParams[key];
      }
    });
  }, debounceDelay);

  /**
   * 清除 URL 参数
   */
  function clearUrlParams() {
    Object.keys(searchParams).forEach(key => {
      if (excludeKeys.includes(key)) return;
      delete urlParams[key];
    });
  }

  /**
   * 清除所有 URL 参数（包括排除的参数）
   */
  function clearAllUrlParams() {
    Object.keys(searchParams).forEach(key => {
      delete urlParams[key];
    });
  }

  // 页面挂载时从 URL 恢复参数
  onMounted(() => {
    if (immediate) {
      restoreFromUrl();
    }
    // 延迟标记初始化完成，确保参数恢复后再开始监听
    nextTick(() => {
      isInitialized.value = true;
    });
  });

  // 监听搜索参数变化，同步到 URL
  watch(
    () => searchParams,
    () => {
      if (isInitialized.value) {
        syncToUrl();
      }
    },
    { deep: true, immediate: false }
  );

  return {
    /** URL 参数对象 */
    urlParams,
    /** 从 URL 恢复搜索参数 */
    restoreFromUrl,
    /** 将搜索参数同步到 URL */
    syncToUrl,
    /** 清除 URL 参数（排除 excludeKeys） */
    clearUrlParams,
    /** 清除所有 URL 参数 */
    clearAllUrlParams
  };
}
