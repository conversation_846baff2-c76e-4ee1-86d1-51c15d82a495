# URL 参数同步解决方案

## 问题描述
当页面有搜索参数时，使用 `router.push()` 跳转到其他页面，再返回时搜索参数会丢失。

## 解决方案

### 1. 使用 useUrlSearchParamsSync Hook

```typescript
import { useUrlSearchParamsSync } from '@/hooks/common/use-url-search-params';

const searchParams = reactive({
  employeeName: null,
  employeeDeptId: null,
  employeePositionId: null
});

const { clearUrlParams, syncToUrlImmediate } = useUrlSearchParamsSync(searchParams, {
  excludeKeys: [],
  debounceDelay: 100  // 减少防抖延迟，确保快速同步
});
```

### 2. 在路由跳转前强制同步

```typescript
import { useRouterWithUrlSync } from '@/hooks/common/use-router-with-url-sync';

// 创建带同步功能的路由 Hook
const { safePush, safeReplace } = useRouterWithUrlSync(syncToUrlImmediate);

// 使用安全的跳转方法
function handleJumpToDetail(id: number) {
  // 这会在跳转前自动同步 URL 参数
  safePush({ path: '/detail', query: { id } });
}
```

### 3. 类型转换修复

Hook 会自动处理 URL 参数的类型转换：
- 包含 'Id' 或 'id' 的字段会自动转换为数字类型
- 布尔值会正确解析
- 复杂对象会使用 JSON 序列化

## 使用示例

```vue
<script setup>
import { reactive } from 'vue';
import { useUrlSearchParamsSync } from '@/hooks/common/use-url-search-params';
import { useRouterWithUrlSync } from '@/hooks/common/use-router-with-url-sync';

const searchParams = reactive({
  name: null,
  deptId: null,  // 这会被正确转换为数字类型
  status: null
});

const { syncToUrlImmediate } = useUrlSearchParamsSync(searchParams);
const { safePush } = useRouterWithUrlSync(syncToUrlImmediate);

function handleViewDetail(id: number) {
  // 跳转前会自动同步当前搜索参数到 URL
  safePush({ path: '/detail', query: { id } });
}
</script>
```

## 注意事项

1. **防抖延迟**：建议设置为 100ms，既保证性能又确保及时同步
2. **excludeKeys**：通常排除 `pageNo` 和 `pageSize`，但如果需要保持分页状态可以设为空数组
3. **类型安全**：ID 字段会自动转换为数字，确保与组件期望的类型匹配
